<?php
// Include configuration
require_once dirname(__DIR__) . '/config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle(); ?></title>
    <meta name="description" content="<?php echo getPageDescription(); ?>">
    <meta name="keywords" content="import, export, logistics, Ukraine, global trade, commodities, grain, wood, shipping">
    <meta name="author" content="PROMTECH EXPORT & IMPORT">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo getPageTitle(); ?>">
    <meta property="og:description" content="<?php echo getPageDescription(); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF7A3D',
                        secondary: '#1E1E1E',
                        'primary-dark': '#E6692F',
                        'gray-50': '#F9FAFB',
                        'gray-100': '#F3F4F6',
                        'gray-600': '#4B5563',
                        'gray-800': '#1F2937',
                        'gray-900': '#111827'
                    },
                    fontFamily: {
                        'pacifico': ['Pacifico', 'cursive']
                    },
                    borderRadius: {
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .dropdown:hover .dropdown-menu {
            display: block;
        }
        
        .hero-bg {
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.85) 0%, rgba(255, 122, 61, 0.15) 100%),
                        url('https://readdy.ai/api/search-image?query=PROMTECH%20export%20import%20logistics%20shipping%20containers%20port%20cranes%20cargo%20ships%20global%20trade%20operations%20Ukraine%20Kiev%20professional%20industrial%20background%20orange%20accent%2C%20high%20quality%2C%20wide%20angle%2C%20cinematic&width=1920&height=1080&seq=promtech-hero001&orientation=landscape');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.7) 0%, rgba(255, 122, 61, 0.1) 100%);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* Hero Section Styles */
        .hero-section {
            background-image: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4)), url('https://readdy.ai/api/search-image?query=logistics%20warehouse%20with%20shipping%20containers%2C%20cranes%2C%20and%20cargo%20ships%20at%20sunset%20with%20warm%20golden%20light%2C%20professional%20photography%2C%20industrial%20scene%2C%20global%20trade%2C%20import%20export%20business%2C%20high%20quality%2C%20detailed%2C%20realistic%2C%20cinematic%20lighting&width=1920&height=1080&seq=logistics001&orientation=landscape');
            background-size: cover;
            background-position: center;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .hover-scale {
            transition: transform 0.3s ease;
        }
        
        .hover-scale:hover {
            transform: scale(1.05);
        }

        /* Testimonial Slider Styles */
        .testimonial-container {
            position: relative;
            overflow: hidden;
        }

        .testimonial-slide {
            display: none;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }

        .testimonial-slide.active {
            display: block;
            opacity: 1;
        }

        .testimonial-dot {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .testimonial-dot:hover {
            background-color: #ff7a3d !important;
        }

        .testimonial-dot.active {
            background-color: #ff7a3d !important;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="bg-secondary text-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.php" class="flex items-center">
                    <div class="text-primary font-pacifico text-2xl mr-2">logo</div>
                    <span class="text-xl font-bold"><?php echo SITE_NAME; ?></span>
                </a>
            </div>
            <nav class="hidden md:flex space-x-6">
                    <?php foreach ($navigation_menu as $key => $item): ?>
                        <?php if (isset($item['submenu'])): ?>
                            <!-- Dropdown Menu -->
                            <div class="dropdown relative group">
                                <button class="flex items-center hover:text-primary">
                                    <?php echo $item['title']; ?>
                                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </button>
                                <div class="dropdown-menu absolute hidden group-hover:block bg-secondary mt-2 py-2 w-48 rounded shadow-lg z-10">
                                    <?php foreach ($item['submenu'] as $sub_key => $sub_item): ?>
                                        <a href="<?php echo $sub_item['url']; ?>"
                                           class="block px-4 py-2 text-white hover:bg-gray-700">
                                            <?php echo $sub_item['title']; ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Regular Menu Item -->
                            <a href="<?php echo $item['url']; ?>"
                               class="hover:text-primary">
                                <?php echo $item['title']; ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </nav>
                
            <div class="hidden md:block">
                <a href="contact.php" class="bg-primary text-white px-4 py-2 rounded-button flex items-center whitespace-nowrap">
                    Get A Quote
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
            </div>
            <div class="md:hidden w-8 h-8 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
            </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="bg-white border-t border-gray-100 py-4">
                <div class="container mx-auto px-4">
                    <nav class="flex flex-col space-y-4">
                        <?php foreach ($navigation_menu as $key => $item): ?>
                            <?php if (isset($item['submenu'])): ?>
                                <!-- Mobile Dropdown -->
                                <div class="mobile-dropdown">
                                    <button class="flex items-center justify-between w-full text-gray-600 hover:text-primary font-medium py-2 mobile-dropdown-btn">
                                        <?php echo $item['title']; ?>
                                        <div class="w-4 h-4 flex items-center justify-center">
                                            <i class="ri-arrow-down-s-line"></i>
                                        </div>
                                    </button>
                                    <div class="mobile-dropdown-menu hidden pl-4 mt-2 space-y-2">
                                        <?php foreach ($item['submenu'] as $sub_key => $sub_item): ?>
                                            <a href="<?php echo $sub_item['url']; ?>" 
                                               class="block text-gray-600 hover:text-primary py-1">
                                                <?php echo $sub_item['title']; ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <a href="<?php echo $item['url']; ?>" 
                                   class="text-gray-600 hover:text-primary font-medium py-2 <?php echo $item['active'] ? 'text-primary' : ''; ?>">
                                    <?php echo $item['title']; ?>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        <a href="contact.php" class="bg-primary text-white px-6 py-3 rounded-button text-center mt-4">
                            Get A Quote
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </header>
