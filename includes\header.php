<?php
// Include configuration
require_once dirname(__DIR__) . '/config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle(); ?></title>
    <meta name="description" content="<?php echo getPageDescription(); ?>">
    <meta name="keywords" content="import, export, logistics, Ukraine, global trade, commodities, grain, wood, shipping">
    <meta name="author" content="PROMTECH EXPORT & IMPORT">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo getPageTitle(); ?>">
    <meta property="og:description" content="<?php echo getPageDescription(); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF7A3D',
                        secondary: '#1E1E1E',
                        'primary-dark': '#E6692F',
                        'gray-50': '#F9FAFB',
                        'gray-100': '#F3F4F6',
                        'gray-600': '#4B5563',
                        'gray-800': '#1F2937',
                        'gray-900': '#111827'
                    },
                    fontFamily: {
                        'pacifico': ['Pacifico', 'cursive']
                    },
                    borderRadius: {
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .dropdown:hover .dropdown-menu {
            display: block;
        }
        
        .hero-bg {
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.85) 0%, rgba(255, 122, 61, 0.15) 100%),
                        url('https://readdy.ai/api/search-image?query=PROMTECH%20export%20import%20logistics%20shipping%20containers%20port%20cranes%20cargo%20ships%20global%20trade%20operations%20Ukraine%20Kiev%20professional%20industrial%20background%20orange%20accent%2C%20high%20quality%2C%20wide%20angle%2C%20cinematic&width=1920&height=1080&seq=promtech-hero001&orientation=landscape');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.7) 0%, rgba(255, 122, 61, 0.1) 100%);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* Slider Styles */
        .hero-slider {
            position: relative;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.8s ease-in-out, visibility 0.8s ease-in-out;
        }

        .slide.active {
            position: relative;
            opacity: 1;
            visibility: visible;
        }

        .slider-dot.active {
            transform: scale(1.2);
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .hover-scale {
            transition: transform 0.3s ease;
        }
        
        .hover-scale:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <a href="index.php" class="flex items-center">
                    <div class="text-primary font-pacifico text-2xl mr-2 drop-shadow-lg">logo</div>
                    <span class="text-xl font-bold text-white drop-shadow-lg"><?php echo SITE_NAME; ?></span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <?php foreach ($navigation_menu as $key => $item): ?>
                        <?php if (isset($item['submenu'])): ?>
                            <!-- Dropdown Menu -->
                            <div class="relative dropdown group">
                                <button class="flex items-center text-white hover:text-primary font-medium transition-colors drop-shadow-lg <?php echo $item['active'] ? 'text-primary' : ''; ?>">
                                    <?php echo $item['title']; ?>
                                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </button>
                                <div class="dropdown-menu absolute hidden group-hover:block bg-white mt-2 py-2 w-48 rounded-lg shadow-lg border border-gray-100 z-10">
                                    <?php foreach ($item['submenu'] as $sub_key => $sub_item): ?>
                                        <a href="<?php echo $sub_item['url']; ?>" 
                                           class="block px-4 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 transition-colors">
                                            <?php echo $sub_item['title']; ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Regular Menu Item -->
                            <a href="<?php echo $item['url']; ?>"
                               class="text-white hover:text-primary font-medium transition-colors drop-shadow-lg <?php echo $item['active'] ? 'text-primary' : ''; ?>">
                                <?php echo $item['title']; ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </nav>
                
                <!-- CTA Button -->
                <a href="contact.php" class="hidden md:block bg-primary text-white px-6 py-3 rounded-button hover:bg-primary-dark transition-colors drop-shadow-lg">
                    Get A Quote
                    <div class="w-4 h-4 inline-flex items-center justify-center ml-1">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden text-white hover:text-primary drop-shadow-lg" id="mobile-menu-btn">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-menu-line ri-lg"></i>
                    </div>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="bg-white border-t border-gray-100 py-4">
                <div class="container mx-auto px-4">
                    <nav class="flex flex-col space-y-4">
                        <?php foreach ($navigation_menu as $key => $item): ?>
                            <?php if (isset($item['submenu'])): ?>
                                <!-- Mobile Dropdown -->
                                <div class="mobile-dropdown">
                                    <button class="flex items-center justify-between w-full text-gray-600 hover:text-primary font-medium py-2 mobile-dropdown-btn">
                                        <?php echo $item['title']; ?>
                                        <div class="w-4 h-4 flex items-center justify-center">
                                            <i class="ri-arrow-down-s-line"></i>
                                        </div>
                                    </button>
                                    <div class="mobile-dropdown-menu hidden pl-4 mt-2 space-y-2">
                                        <?php foreach ($item['submenu'] as $sub_key => $sub_item): ?>
                                            <a href="<?php echo $sub_item['url']; ?>" 
                                               class="block text-gray-600 hover:text-primary py-1">
                                                <?php echo $sub_item['title']; ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <a href="<?php echo $item['url']; ?>" 
                                   class="text-gray-600 hover:text-primary font-medium py-2 <?php echo $item['active'] ? 'text-primary' : ''; ?>">
                                    <?php echo $item['title']; ?>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        <a href="contact.php" class="bg-primary text-white px-6 py-3 rounded-button text-center mt-4">
                            Get A Quote
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </header>
