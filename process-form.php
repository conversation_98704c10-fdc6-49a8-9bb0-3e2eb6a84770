<?php
require_once 'config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get form type from URL parameter
$formType = isset($_GET['type']) ? $_GET['type'] : 'contact';

try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    // If JSON decode fails, try regular POST data
    if (!$input) {
        $input = $_POST;
    }
    
    // Validate required fields based on form type
    $requiredFields = [];
    $emailSubject = '';
    $emailTemplate = '';
    
    if ($formType === 'quote') {
        $requiredFields = ['firstName', 'lastName', 'email', 'phone', 'originCountry', 'destination', 'serviceType'];
        $emailSubject = 'New Quote Request from ' . SITE_NAME;
        $emailTemplate = 'quote_email_template';
    } else {
        $requiredFields = ['firstName', 'lastName', 'email', 'phone', 'subject', 'message'];
        $emailSubject = 'New Contact Form Submission from ' . SITE_NAME;
        $emailTemplate = 'contact_email_template';
    }
    
    // Validate required fields
    $missingFields = [];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            $missingFields[] = $field;
        }
    }
    
    if (!empty($missingFields)) {
        echo json_encode([
            'success' => false, 
            'message' => 'Missing required fields: ' . implode(', ', $missingFields)
        ]);
        exit;
    }
    
    // Validate email format
    if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit;
    }
    
    // Sanitize input data
    $sanitizedData = [];
    foreach ($input as $key => $value) {
        $sanitizedData[$key] = htmlspecialchars(strip_tags(trim($value)), ENT_QUOTES, 'UTF-8');
    }
    
    // Generate email content
    $emailContent = generateEmailContent($emailTemplate, $sanitizedData);
    
    // Email headers
    $headers = [
        'From: ' . SITE_EMAIL,
        'Reply-To: ' . $sanitizedData['email'],
        'X-Mailer: PHP/' . phpversion(),
        'MIME-Version: 1.0',
        'Content-Type: text/html; charset=UTF-8'
    ];

    // Send email
    $emailSent = mail(SITE_EMAIL, $emailSubject, $emailContent, implode("\r\n", $headers));
    
    if ($emailSent) {
        // Log the submission (optional)
        logFormSubmission($formType, $sanitizedData);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Thank you! Your ' . ($formType === 'quote' ? 'quote request' : 'message') . ' has been sent successfully. We will get back to you within 24 hours.'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Sorry, there was an error sending your message. Please try again or contact us directly.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Form processing error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred. Please try again later.'
    ]);
}

function generateEmailContent($template, $data) {
    if ($template === 'quote_email_template') {
        return generateQuoteEmailTemplate($data);
    } else {
        return generateContactEmailTemplate($data);
    }
}

function generateQuoteEmailTemplate($data) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Quote Request</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1E3A8A, #FF7A3D); color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #1E3A8A; }
            .value { margin-left: 10px; }
            .footer { background: #1E3A8A; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . SITE_NAME . '</h1>
                <h2>New Quote Request</h2>
            </div>
            <div class="content">
                <h3>Customer Information</h3>
                <div class="field"><span class="label">Name:</span><span class="value">' . $data['firstName'] . ' ' . $data['lastName'] . '</span></div>
                <div class="field"><span class="label">Email:</span><span class="value">' . $data['email'] . '</span></div>
                <div class="field"><span class="label">Phone:</span><span class="value">' . $data['phone'] . '</span></div>';
    
    if (!empty($data['company'])) {
        $html .= '<div class="field"><span class="label">Company:</span><span class="value">' . $data['company'] . '</span></div>';
    }
    
    $html .= '
                <h3>Shipping Details</h3>
                <div class="field"><span class="label">Origin:</span><span class="value">' . $data['originCountry'] . '</span></div>
                <div class="field"><span class="label">Destination:</span><span class="value">' . $data['destination'] . '</span></div>
                <div class="field"><span class="label">Service Type:</span><span class="value">' . $data['serviceType'] . '</span></div>';
    
    if (!empty($data['weight'])) {
        $html .= '<div class="field"><span class="label">Weight:</span><span class="value">' . $data['weight'] . ' kg</span></div>';
    }
    
    if (!empty($data['length']) || !empty($data['width']) || !empty($data['height'])) {
        $dimensions = [];
        if (!empty($data['length'])) $dimensions[] = 'L: ' . $data['length'] . 'cm';
        if (!empty($data['width'])) $dimensions[] = 'W: ' . $data['width'] . 'cm';
        if (!empty($data['height'])) $dimensions[] = 'H: ' . $data['height'] . 'cm';
        $html .= '<div class="field"><span class="label">Dimensions:</span><span class="value">' . implode(', ', $dimensions) . '</span></div>';
    }
    
    if (!empty($data['commodityType'])) {
        $html .= '<div class="field"><span class="label">Commodity:</span><span class="value">' . $data['commodityType'] . '</span></div>';
    }
    
    if (!empty($data['pickupDate'])) {
        $html .= '<div class="field"><span class="label">Preferred Pickup Date:</span><span class="value">' . $data['pickupDate'] . '</span></div>';
    }
    
    if (!empty($data['requirements'])) {
        $html .= '<div class="field"><span class="label">Additional Requirements:</span><div style="margin-top: 5px; padding: 10px; background: white; border-left: 3px solid #FF7A3D;">' . nl2br($data['requirements']) . '</div></div>';
    }
    
    $html .= '
            </div>
            <div class="footer">
                <p>This quote request was submitted on ' . date('Y-m-d H:i:s') . '</p>
                <p>' . SITE_EMAIL . ' | ' . COMPANY_PHONE . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

function generateContactEmailTemplate($data) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1E3A8A, #FF7A3D); color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #1E3A8A; }
            .value { margin-left: 10px; }
            .message { background: white; padding: 15px; border-left: 3px solid #FF7A3D; margin-top: 10px; }
            .footer { background: #1E3A8A; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . SITE_NAME . '</h1>
                <h2>New Contact Form Submission</h2>
            </div>
            <div class="content">
                <div class="field"><span class="label">Name:</span><span class="value">' . $data['firstName'] . ' ' . $data['lastName'] . '</span></div>
                <div class="field"><span class="label">Email:</span><span class="value">' . $data['email'] . '</span></div>
                <div class="field"><span class="label">Phone:</span><span class="value">' . $data['phone'] . '</span></div>';
    
    if (!empty($data['company'])) {
        $html .= '<div class="field"><span class="label">Company:</span><span class="value">' . $data['company'] . '</span></div>';
    }
    
    $html .= '
                <div class="field"><span class="label">Subject:</span><span class="value">' . $data['subject'] . '</span></div>
                <div class="field">
                    <span class="label">Message:</span>
                    <div class="message">' . nl2br($data['message']) . '</div>
                </div>
            </div>
            <div class="footer">
                <p>This message was submitted on ' . date('Y-m-d H:i:s') . '</p>
                <p>' . SITE_EMAIL . ' | ' . COMPANY_PHONE . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

function logFormSubmission($type, $data) {
    $logFile = 'logs/form_submissions.log';
    $logDir = dirname($logFile);
    
    // Create logs directory if it doesn't exist
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'data' => $data
    ];
    
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}
?>
