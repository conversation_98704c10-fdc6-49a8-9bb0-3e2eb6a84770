    <!-- Footer -->
    <footer class="bg-gray-50 text-gray-800 pt-16 pb-8">
        <div class="container mx-auto px-4">
            <!-- Newsletter Section -->
            <div class="bg-primary rounded-2xl p-8 mb-12 text-center">
                <h3 class="text-2xl font-bold text-white mb-4">Subscribe Our Newsletter</h3>
                <p class="text-white/90 mb-6 max-w-2xl mx-auto">
                    Stay updated with the latest logistics trends, shipping updates, and industry insights. Get exclusive offers and expert tips delivered to your inbox.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address"
                           class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white">
                    <button class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Subscribe
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center mb-6">
                        <div class="text-primary font-pacifico text-2xl mr-2">logo</div>
                        <span class="text-xl font-bold"><?php echo SITE_NAME; ?></span>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Leading platform for global trade solutions, connecting markets and empowering businesses worldwide with reliable logistics services.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-facebook-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-twitter-x-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-linkedin-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-instagram-line"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Company -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Company</h3>
                    <ul class="space-y-3">
                        <li><a href="about.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>About Us
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Our Services
                        </a></li>
                        <li><a href="commodities.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Commodities
                        </a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Our Team
                        </a></li>
                        <li><a href="contact.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Contact Us
                        </a></li>
                    </ul>
                </div>
                
                <!-- Services -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Services</h3>
                    <ul class="space-y-3">
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Ocean Freight
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Air Freight
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Road Freight
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Warehousing
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Custom Clearance
                        </a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Contact Info</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-map-pin-line"></i>
                            </div>
                            <div>
                                <p class="text-gray-600 leading-relaxed">74 Hrushevsky Street,<br>Kiev, 01008, Ukraine</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-mail-line"></i>
                            </div>
                            <div>
                                <a href="mailto:<EMAIL>" class="text-gray-600 hover:text-primary transition-colors">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-phone-line"></i>
                            </div>
                            <div>
                                <a href="tel:<?php echo str_replace(' ', '', COMPANY_PHONE); ?>" class="text-gray-600 hover:text-primary transition-colors">
                                    <?php echo COMPANY_PHONE; ?>
                                </a>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-time-line"></i>
                            </div>
                            <div>
                                <p class="text-gray-600">Mon - Fri: 9:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 4:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="border-t border-gray-300 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-600 mb-4 md:mb-0">
                        © <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-600 hover:text-primary transition-colors">Privacy Policy</a>
                        <a href="#" class="text-gray-600 hover:text-primary transition-colors">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // Mobile dropdown toggles
        document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const dropdown = this.nextElementSibling;
                const icon = this.querySelector('i');
                
                dropdown.classList.toggle('hidden');
                icon.classList.toggle('ri-arrow-down-s-line');
                icon.classList.toggle('ri-arrow-up-s-line');
            });
        });
        
        // Newsletter form submission
        document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                alert('Thank you for subscribing to our newsletter!');
                this.reset();
            }
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Add animation classes on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.hover-scale, .bg-white').forEach(el => {
            observer.observe(el);
        });

        // Simple animations and interactions can be added here if needed

        // Testimonial Slider Functionality
        let currentTestimonial = 0;
        const testimonialSlides = document.querySelectorAll('.testimonial-slide');
        const testimonialDots = document.querySelectorAll('.testimonial-dot');

        function showTestimonial(index) {
            // Hide all slides
            testimonialSlides.forEach(slide => {
                slide.classList.remove('active');
            });

            // Remove active class from all dots
            testimonialDots.forEach(dot => {
                dot.classList.remove('active');
                dot.classList.add('bg-gray-300');
                dot.classList.remove('bg-primary');
            });

            // Show current slide
            if (testimonialSlides[index]) {
                testimonialSlides[index].classList.add('active');
            }

            // Activate current dot
            if (testimonialDots[index]) {
                testimonialDots[index].classList.add('active');
                testimonialDots[index].classList.remove('bg-gray-300');
                testimonialDots[index].classList.add('bg-primary');
            }
        }

        // Add click event listeners to dots
        testimonialDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentTestimonial = index;
                showTestimonial(currentTestimonial);
            });
        });

        // Auto-advance testimonials every 5 seconds
        setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonialSlides.length;
            showTestimonial(currentTestimonial);
        }, 5000);

        // Initialize first testimonial
        if (testimonialSlides.length > 0) {
            showTestimonial(0);
        }

        // FAQ Accordion Functionality
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', () => {
                const faqItem = question.parentElement;
                const answer = faqItem.querySelector('.faq-answer');
                const icon = question.querySelector('.faq-icon');

                // Toggle current FAQ
                const isOpen = !answer.classList.contains('hidden');

                if (isOpen) {
                    answer.classList.add('hidden');
                    icon.classList.remove('ri-arrow-up-line');
                    icon.classList.add('ri-arrow-down-line');
                } else {
                    answer.classList.remove('hidden');
                    icon.classList.remove('ri-arrow-down-line');
                    icon.classList.add('ri-arrow-up-line');
                }
            });
        });
    </script>
</body>
</html>
