    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center mb-6">
                        <div class="text-primary font-pacifico text-2xl mr-2">logo</div>
                        <span class="text-xl font-bold"><?php echo SITE_NAME; ?></span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        Leading platform for global trade solutions, connecting markets and empowering businesses worldwide.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-facebook-fill"></i>
                            </div>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-twitter-x-fill"></i>
                            </div>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-linkedin-fill"></i>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Quick Links</h3>
                    <ul class="space-y-3">
                        <?php foreach ($navigation_menu as $key => $item): ?>
                            <li>
                                <a href="<?php echo $item['url']; ?>" class="text-gray-400 hover:text-primary transition-colors">
                                    <?php echo $item['title']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Our Services -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Our Services</h3>
                    <ul class="space-y-3">
                        <li><a href="services.php" class="text-gray-400 hover:text-primary transition-colors">Export/Import Operations</a></li>
                        <li><a href="services.php" class="text-gray-400 hover:text-primary transition-colors">Documentation Support</a></li>
                        <li><a href="services.php" class="text-gray-400 hover:text-primary transition-colors">Custom Clearance</a></li>
                        <li><a href="services.php" class="text-gray-400 hover:text-primary transition-colors">Freight Handling</a></li>
                        <li><a href="services.php" class="text-gray-400 hover:text-primary transition-colors">Logistics & Shipping</a></li>
                        <li><a href="services.php" class="text-gray-400 hover:text-primary transition-colors">Warehousing</a></li>
                    </ul>
                </div>
                
                <!-- Contact & Newsletter -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Contact Info</h3>
                    <div class="space-y-4 mb-6">
                        <div class="flex items-start">
                            <div class="w-5 h-5 flex items-center justify-center text-primary mt-1 mr-3">
                                <i class="ri-map-pin-line"></i>
                            </div>
                            <p class="text-gray-400"><?php echo COMPANY_ADDRESS; ?></p>
                        </div>
                        <div class="flex items-center">
                            <div class="w-5 h-5 flex items-center justify-center text-primary mr-3">
                                <i class="ri-mail-line"></i>
                            </div>
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-gray-400 hover:text-primary transition-colors">
                                <?php echo SITE_EMAIL; ?>
                            </a>
                        </div>
                        <div class="flex items-center">
                            <div class="w-5 h-5 flex items-center justify-center text-primary mr-3">
                                <i class="ri-phone-line"></i>
                            </div>
                            <a href="tel:<?php echo COMPANY_PHONE; ?>" class="text-gray-400 hover:text-primary transition-colors">
                                <?php echo COMPANY_PHONE; ?>
                            </a>
                        </div>
                    </div>
                    
                    <h4 class="text-lg font-bold mb-4">Newsletter</h4>
                    <p class="text-gray-400 mb-4 text-sm">
                        Subscribe to receive updates on our services and industry insights.
                    </p>
                    <form class="newsletter-form">
                        <div class="flex mb-4">
                            <input type="email" placeholder="Your email address" 
                                   class="bg-gray-800 border-none text-white px-4 py-3 rounded-l w-full focus:outline-none focus:ring-2 focus:ring-primary" required>
                            <button type="submit" class="bg-primary text-white px-4 py-3 rounded-r hover:bg-primary-dark transition-colors">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-send-plane-fill"></i>
                                </div>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 mb-4 md:mb-0">
                        © <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-primary transition-colors">Privacy Policy</a>
                        <a href="#" class="text-gray-400 hover:text-primary transition-colors">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // Mobile dropdown toggles
        document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const dropdown = this.nextElementSibling;
                const icon = this.querySelector('i');
                
                dropdown.classList.toggle('hidden');
                icon.classList.toggle('ri-arrow-down-s-line');
                icon.classList.toggle('ri-arrow-up-s-line');
            });
        });
        
        // Newsletter form submission
        document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                alert('Thank you for subscribing to our newsletter!');
                this.reset();
            }
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Add animation classes on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.hover-scale, .bg-white').forEach(el => {
            observer.observe(el);
        });

        // Hero Slider Functionality
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.slider-dot');
        const totalSlides = slides.length;

        function showSlide(index) {
            // Remove active class from all slides and dots
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            // Add active class to current slide and dot
            if (slides[index]) {
                slides[index].classList.add('active');
            }
            if (dots[index]) {
                dots[index].classList.add('active');
            }
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        // Auto-play slider
        setInterval(nextSlide, 8000); // Change slide every 8 seconds

        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
            });
        });

        // Keyboard navigation (optional)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') prevSlide();
            if (e.key === 'ArrowRight') nextSlide();
        });
    </script>
</body>
</html>
