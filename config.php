<?php
// PROMTECH EXPORT & IMPORT - Configuration File
// Website configuration and constants

// Site Information
define('SITE_NAME', 'PROMTECH EXPORT & IMPORT');
define('SITE_TAGLINE', 'Global Trade Solutions');
define('SITE_URL', 'https://www.promtech-export-import.com');
define('SITE_EMAIL', '<EMAIL>');
define('CEO_EMAIL', '<EMAIL>');

// Company Information
define('COMPANY_NAME', 'PROMTECH EXPORT & IMPORT');
define('COMPANY_ADDRESS', '74 Hrushevsky Street, Kiev, 01008, Ukraine');
define('COMPANY_PHONE', '+380 44 555 0123');
define('CEO_NAME', 'Mr. Petrov Denysenko');

// Navigation Menu Structure
$navigation_menu = [
    'home' => [
        'title' => 'Home',
        'url' => 'index.php',
        'active' => false
    ],
    'about' => [
        'title' => 'About Us', 
        'url' => 'about.php',
        'active' => false
    ],
    'commodities' => [
        'title' => 'Commodities',
        'url' => 'commodities.php', 
        'active' => false
    ],
    'markets' => [
        'title' => 'Markets',
        'url' => '#',
        'active' => false,
        'submenu' => [
            'grain' => [
                'title' => 'Grain Market',
                'url' => 'grain-market.php'
            ],
            'wood' => [
                'title' => 'Wood Market', 
                'url' => 'wood-market.php'
            ]
        ]
    ],
    'services' => [
        'title' => 'Services',
        'url' => 'services.php',
        'active' => false
    ],
    'contact' => [
        'title' => 'Contact Us',
        'url' => 'contact.php', 
        'active' => false
    ]
];

// Set active page based on current file
function setActivePage($current_page) {
    global $navigation_menu;
    
    foreach ($navigation_menu as $key => &$item) {
        $item['active'] = false;
        
        // Check main menu items
        if (basename($_SERVER['PHP_SELF']) === $item['url']) {
            $item['active'] = true;
        }
        
        // Check submenu items
        if (isset($item['submenu'])) {
            foreach ($item['submenu'] as $sub_key => &$sub_item) {
                if (basename($_SERVER['PHP_SELF']) === $sub_item['url']) {
                    $item['active'] = true;
                    $sub_item['active'] = true;
                }
            }
        }
    }
}

// Helper function to get page title
function getPageTitle($page = '') {
    $titles = [
        'index.php' => 'Home - Leading Platform for Global Trade Solutions',
        'about.php' => 'About Us - Company Profile & Leadership',
        'commodities.php' => 'Our Commodities - Agricultural, Chemical, Mineral Products',
        'grain-market.php' => 'Grain Market - Ukraine Grain Exports',
        'wood-market.php' => 'Wood Market - Timber & Sustainable Sourcing',
        'services.php' => 'Our Services - End-to-End Trade Operations',
        'contact.php' => 'Contact Us - Get in Touch'
    ];
    
    $current_page = basename($_SERVER['PHP_SELF']);
    return isset($titles[$current_page]) ? $titles[$current_page] : SITE_NAME . ' - ' . SITE_TAGLINE;
}

// Helper function to get page description
function getPageDescription($page = '') {
    $descriptions = [
        'index.php' => 'PROMTECH EXPORT & IMPORT - Leading Ukrainian logistics company specializing in global trade solutions, import-export operations, and supply chain management.',
        'about.php' => 'Learn about PROMTECH leadership, company history, mission & vision. Meet CEO Mr. Petrov Denysenko and discover our global network.',
        'commodities.php' => 'Explore our wide range of commodities: agricultural products, chemical goods, mineral products, wood & timber, metal scrap, machinery and tools.',
        'grain-market.php' => 'Ukraine grain market overview, export trends, and available grain products from PROMTECH EXPORT & IMPORT.',
        'wood-market.php' => 'Sustainable timber sourcing, wood market analysis, and certified wood products from Ukrainian forests.',
        'services.php' => 'Comprehensive trade services: export/import documentation, customs clearance, freight handling, logistics & warehousing.',
        'contact.php' => 'Contact PROMTECH EXPORT & IMPORT. Get in touch with our team in Kiev, Ukraine for global trade solutions.'
    ];
    
    $current_page = basename($_SERVER['PHP_SELF']);
    return isset($descriptions[$current_page]) ? $descriptions[$current_page] : 'PROMTECH EXPORT & IMPORT - Global Trade Solutions';
}

// Initialize active page
setActivePage(basename($_SERVER['PHP_SELF']));
?>
